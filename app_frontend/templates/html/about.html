{% extends 'html/base.html' %}
{% load static %}

{% block title %}About Us - HiSage Health{% endblock %}

{% block description %}Meet the team behind HiSage Health and learn about our journey in revolutionizing dementia screening through AI technology.{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&display=swap" rel="stylesheet">
<style>
    :root {
        --primary-blue: #2563eb;
        --primary-blue-dark: #1d4ed8;
        --secondary-blue: #3b82f6;
        --accent-teal: #0d9488;
        --accent-green: #059669;
        --text-dark: #1f2937;
        --text-gray: #6b7280;
        --text-light: #9ca3af;
        --bg-light: #f8fafc;
        --bg-white: #ffffff;
        --border-light: #e5e7eb;
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        color: var(--text-dark);
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section {
        padding: 80px 0;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 1rem;
        color: var(--text-dark);
    }

    .section-subtitle {
        font-size: 1.2rem;
        text-align: center;
        color: var(--text-gray);
        margin-bottom: 3rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Hero Section */
    .hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 120px 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
        opacity: 0.3;
    }

    .hero-content {
        position: relative;
        z-index: 1;
    }

    .hero h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }

    .hero p {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }

    .cta-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: var(--accent-teal);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-lg);
        border: none;
        cursor: pointer;
        font-family: inherit;
    }

    .cta-button:hover {
        background: var(--accent-green);
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
        color: white;
        text-decoration: none;
    }

    /* Stats Grid */
    .stats {
        background: var(--bg-light);
        padding: 60px 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        text-align: center;
    }

    .stat-item {
        background: white;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: var(--shadow-sm);
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        color: var(--primary-blue);
        margin-bottom: 0.5rem;
    }

    .stat-label {
        font-size: 1rem;
        color: var(--text-gray);
        font-weight: 500;
    }

    /* Features Grid */
    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
        border: 1px solid var(--border-light);
    }

    .feature-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1.5rem;
    }

    .feature-icon i {
        font-size: 1.5rem;
        color: white;
    }

    .feature-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-dark);
    }

    .feature-description {
        color: var(--text-gray);
        line-height: 1.6;
    }

    /* Team Cards */
    .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 3rem;
    }

    .team-card {
        background: var(--bg-light);
        padding: 2rem;
        border-radius: 12px;
        text-align: center;
        box-shadow: var(--shadow-sm);
        transition: all 0.3s ease;
    }

    .team-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-md);
    }

    .team-avatar {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
        border-radius: 50%;
        margin: 0 auto 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .team-avatar i {
        font-size: 2rem;
        color: white;
    }

    .team-name {
        color: var(--text-dark);
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .team-description {
        color: var(--text-gray);
        line-height: 1.6;
        margin-bottom: 1.5rem;
        font-size: 0.9rem;
    }

    .team-stats {
        background: white;
        padding: 1rem;
        border-radius: 8px;
    }

    .team-stats div {
        font-size: 0.9rem;
        color: var(--text-gray);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.5rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .features-grid {
            grid-template-columns: 1fr;
        }

        .team-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }

        .hero h1 {
            font-size: 2rem;
        }

        .stat-number {
            font-size: 2rem;
        }
    }





    /* Responsive Design */
    @media (max-width: 768px) {
        .hero h1 {
            font-size: 2.5rem;
        }

        .hero p {
            font-size: 1.1rem;
        }

        .section-title {
            font-size: 2rem;
        }

        .mission-grid {
            grid-template-columns: 1fr;
            gap: 2rem;
        }

        /* The Beginning section responsive */
        .container > div[style*="grid-template-columns: 1fr 1fr"] {
            grid-template-columns: 1fr !important;
            gap: 2rem !important;
        }

        .team-grid {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .values-grid {
            grid-template-columns: 1fr;
        }

        .tech-grid {
            grid-template-columns: 1fr;
        }

        .container {
            padding: 0 15px;
        }

        .section {
            padding: 60px 0;
        }

        .hero {
            padding: 100px 0 60px;
        }
    }

    @media (max-width: 480px) {
        .hero h1 {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .stat-number {
            font-size: 2rem;
        }

        .team-card {
            padding: 2rem;
        }

        .value-card {
            padding: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
    <!-- The Beginning Section -->
    <section class="section" style="background: white; padding-top: 120px;">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: flex-start; margin-top: 3rem;">
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">The Beginning</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        HiSage Health was founded in 2020 by a team of passionate researchers and clinicians who witnessed the devastating impact of late-stage dementia diagnosis on countless families. Frustrated by the limitations of traditional cognitive assessments, our founders brought together expertise in neurology, artificial intelligence, and speech pathology.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        Our founders shared a vision: to harness the power of artificial intelligence and speech analysis to detect cognitive decline years before symptoms become apparent to families and doctors.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        What started as a research project in a small university lab has grown into a comprehensive platform that has screened over 50,000 individuals worldwide, helping families gain precious time for planning and intervention.
                    </p>
                </div>
                <div>
                    <h3 style="color: var(--text-dark); font-size: 1.8rem; margin-bottom: 1.5rem;">Our Mission</h3>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        To democratize early dementia detection through cutting-edge AI technology, making cognitive health screening accessible, accurate, and affordable for everyone worldwide.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8; margin-bottom: 1.5rem;">
                        We believe that early detection shouldn't be a privilege reserved for those with access to specialized medical centers. Our AI-powered platform democratizes cognitive health screening, making it accessible to anyone, anywhere.
                    </p>
                    <p style="color: var(--text-gray); line-height: 1.8;">
                        Through the power of speech analysis and machine learning, we're not just detecting dementia earlier – we're giving families precious time to plan, prepare, and pursue treatments that could slow progression.
                    </p>
                </div>
            </div>
        </div>
    </section>



    <!-- Our Technology Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Our Technology</h2>
            <p class="section-subtitle">Advanced AI algorithms that power early dementia detection</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <!-- Speech Analysis Engine -->
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #2563eb;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; border: 2px solid #2563eb; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-chart-bar" style="font-size: 1.2rem; color: #2563eb;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Speech Analysis Engine</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">Core AI Technology</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Our proprietary algorithms analyze over 500 speech features including pause patterns, semantic fluency, and linguistic complexity to detect subtle cognitive changes years before traditional methods.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Key Features:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-chart-bar" style="font-size: 0.8rem; margin-right: 0.5rem; color: #2563eb;"></i>500+ speech biomarkers analyzed</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-bolt" style="font-size: 0.8rem; margin-right: 0.5rem; color: #2563eb;"></i>Real-time processing capability</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-globe" style="font-size: 0.8rem; margin-right: 0.5rem; color: #2563eb;"></i>Multi-language support</li>
                        <li><i class="fas fa-stethoscope" style="font-size: 0.8rem; margin-right: 0.5rem; color: #2563eb;"></i>Clinical validation across populations</li>
                    </ul>
                </div>

                <!-- Machine Learning Models -->
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #0d9488;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; border: 2px solid #0d9488; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-brain" style="font-size: 1.2rem; color: #0d9488;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Machine Learning Models</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">Deep Learning Architecture</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Trained on datasets from over 10,000 participants, our deep learning models achieve 95.2% accuracy in distinguishing between healthy cognition and early-stage dementia.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Key Features:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-bullseye" style="font-size: 0.8rem; margin-right: 0.5rem; color: #0d9488;"></i>95.2% detection accuracy</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-database" style="font-size: 0.8rem; margin-right: 0.5rem; color: #0d9488;"></i>10,000+ training samples</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-graduation-cap" style="font-size: 0.8rem; margin-right: 0.5rem; color: #0d9488;"></i>Continuous learning capability</li>
                        <li><i class="fas fa-network-wired" style="font-size: 0.8rem; margin-right: 0.5rem; color: #0d9488;"></i>Federated learning architecture</li>
                    </ul>
                </div>

                <!-- Predictive Analytics -->
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #7c3aed;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; border: 2px solid #7c3aed; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-chart-line" style="font-size: 1.2rem; color: #7c3aed;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Predictive Analytics</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">Future Risk Assessment</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Our platform doesn't just detect current cognitive status – it predicts future decline risk, enabling proactive intervention strategies and personalized care planning.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Key Features:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-clock" style="font-size: 0.8rem; margin-right: 0.5rem; color: #7c3aed;"></i>3-7 years early detection</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-layer-group" style="font-size: 0.8rem; margin-right: 0.5rem; color: #7c3aed;"></i>Risk stratification models</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-user-cog" style="font-size: 0.8rem; margin-right: 0.5rem; color: #7c3aed;"></i>Personalized recommendations</li>
                        <li><i class="fas fa-chart-area" style="font-size: 0.8rem; margin-right: 0.5rem; color: #7c3aed;"></i>Longitudinal tracking</li>
                    </ul>
                </div>

                <!-- Privacy & Security -->
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-md); border-left: 4px solid #dc2626;">
                    <div style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1.5rem;">
                        <div style="width: 50px; height: 50px; border: 2px solid #dc2626; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-shield-alt" style="font-size: 1.2rem; color: #dc2626;"></i>
                        </div>
                        <div>
                            <h3 style="color: var(--text-dark); margin: 0; font-size: 1.25rem;">Privacy-First Design</h3>
                            <p style="color: var(--text-gray); margin: 0; font-size: 0.9rem;">Enterprise Security</p>
                        </div>
                    </div>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Built with enterprise-grade security and HIPAA compliance. All data is encrypted end-to-end, and our federated learning approach ensures privacy while improving model performance.
                    </p>
                    <h5 style="color: var(--text-dark); margin-bottom: 0.75rem;">Key Features:</h5>
                    <ul style="list-style: none; padding: 0; color: var(--text-gray); font-size: 0.9rem;">
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-hospital" style="font-size: 0.8rem; margin-right: 0.5rem; color: #dc2626;"></i>HIPAA compliant infrastructure</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-lock" style="font-size: 0.8rem; margin-right: 0.5rem; color: #dc2626;"></i>End-to-end encryption</li>
                        <li style="margin-bottom: 0.5rem;"><i class="fas fa-eye-slash" style="font-size: 0.8rem; margin-right: 0.5rem; color: #dc2626;"></i>Zero-knowledge architecture</li>
                        <li><i class="fas fa-certificate" style="font-size: 0.8rem; margin-right: 0.5rem; color: #dc2626;"></i>SOC 2 Type II certified</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Company Milestones Section -->
    <section class="section" style="background: var(--bg-light);">
        <div class="container">
            <h2 class="section-title">Company Milestones</h2>
            <p class="section-subtitle">Key achievements in our journey to transform dementia screening</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <!-- FDA Breakthrough -->
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm); text-align: center;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-award" style="font-size: 2rem; color: white;"></i>
                    </div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1rem;">FDA Breakthrough Device</h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        First AI speech analysis platform to receive FDA Breakthrough Device Designation for dementia screening, recognizing our innovative approach to early detection.
                    </p>
                    <div style="background: var(--bg-light); padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 0.9rem; color: var(--text-gray);">
                            <strong>2023</strong> FDA Recognition<br>
                            <strong>First</strong> of its kind<br>
                            <strong>Breakthrough</strong> designation
                        </div>
                    </div>
                </div>

                <!-- Clinical Validation -->
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm); text-align: center;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--accent-teal), var(--accent-green)); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-flask" style="font-size: 2rem; color: white;"></i>
                    </div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1rem;">Clinical Validation</h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Completed multi-site clinical trials with 2,500 participants across 12 medical centers, demonstrating 95.2% accuracy in early dementia detection.
                    </p>
                    <div style="background: var(--bg-light); padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 0.9rem; color: var(--text-gray);">
                            <strong>2,500+</strong> Participants<br>
                            <strong>12</strong> Medical centers<br>
                            <strong>95.2%</strong> Accuracy
                        </div>
                    </div>
                </div>

                <!-- Global Expansion -->
                <div style="background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm); text-align: center;">
                    <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #7c3aed, #8b5cf6); border-radius: 50%; margin: 0 auto 1.5rem; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-globe" style="font-size: 2rem; color: white;"></i>
                    </div>
                    <h3 style="color: var(--text-dark); margin-bottom: 1rem;">Global Expansion</h3>
                    <p style="color: var(--text-gray); line-height: 1.6; margin-bottom: 1.5rem;">
                        Launched international operations in Europe and Asia, establishing partnerships with healthcare systems in 15 countries worldwide.
                    </p>
                    <div style="background: var(--bg-light); padding: 1rem; border-radius: 8px;">
                        <div style="font-size: 0.9rem; color: var(--text-gray);">
                            <strong>15</strong> Countries<br>
                            <strong>50,000+</strong> Screened<br>
                            <strong>Global</strong> Impact
                        </div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 3rem; background: #fef3c7; padding: 2rem; border-radius: 12px; border-left: 4px solid #f59e0b;">
                <h4 style="color: #92400e; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-rocket" style="color: #f59e0b;"></i>
                    What's Next
                </h4>
                <p style="color: #92400e; line-height: 1.7;">
                    We're preparing to launch HiSage 2.0 with enhanced multilingual support, real-time analysis, and integration with electronic health records. Our Series B funding round will accelerate global adoption and expand our research into other neurological conditions.
                </p>
            </div>
        </div>
    </section>

    <!-- Impact & Recognition Section -->
    <section class="section" style="background: white;">
        <div class="container">
            <h2 class="section-title">Global Impact & Recognition</h2>
            <p class="section-subtitle">Our achievements in transforming cognitive healthcare worldwide</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; margin-top: 3rem;">
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm); position: relative;">
                    <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); width: 50px; height: 50px; background: linear-gradient(135deg, #dc2626, #ef4444); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);">
                        <i class="fas fa-dollar-sign" style="font-size: 1.2rem; color: white;"></i>
                    </div>
                    <div style="margin-top: 1rem;">
                        <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">$1.3T</div>
                        <div style="color: var(--text-gray); font-weight: 500;">Annual Global Cost</div>
                        <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Economic burden of dementia worldwide</div>
                    </div>
                </div>
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm); position: relative;">
                    <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); width: 50px; height: 50px; background: linear-gradient(135deg, #2563eb, #3b82f6); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);">
                        <i class="fas fa-users" style="font-size: 1.2rem; color: white;"></i>
                    </div>
                    <div style="margin-top: 1rem;">
                        <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">55M</div>
                        <div style="color: var(--text-gray); font-weight: 500;">People Affected</div>
                        <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Living with dementia globally</div>
                    </div>
                </div>
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm); position: relative;">
                    <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); width: 50px; height: 50px; background: linear-gradient(135deg, #0d9488, #059669); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 8px rgba(13, 148, 136, 0.3);">
                        <i class="fas fa-plus-circle" style="font-size: 1.2rem; color: white;"></i>
                    </div>
                    <div style="margin-top: 1rem;">
                        <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">10M</div>
                        <div style="color: var(--text-gray); font-weight: 500;">New Cases</div>
                        <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Diagnosed annually worldwide</div>
                    </div>
                </div>
                <div style="text-align: center; background: white; padding: 2rem; border-radius: 12px; box-shadow: var(--shadow-sm); position: relative;">
                    <div style="position: absolute; top: -15px; left: 50%; transform: translateX(-50%); width: 50px; height: 50px; background: linear-gradient(135deg, #7c3aed, #8b5cf6); border-radius: 50%; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 8px rgba(124, 58, 237, 0.3);">
                        <i class="fas fa-clock" style="font-size: 1.2rem; color: white;"></i>
                    </div>
                    <div style="margin-top: 1rem;">
                        <div style="font-size: 2.5rem; color: var(--primary-blue); margin-bottom: 0.5rem; font-weight: 700;">3-7</div>
                        <div style="color: var(--text-gray); font-weight: 500;">Years Earlier</div>
                        <div style="color: var(--text-light); font-size: 0.9rem; margin-top: 0.5rem;">Our detection vs traditional methods</div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 4rem;">
                <h3 style="color: var(--text-dark); text-align: center; margin-bottom: 2rem;">Institutional Partners</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                    <div style="text-align: center; background: var(--bg-light); padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-university" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Top Universities</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem;">MIT, Stanford, Johns Hopkins, Harvard Medical School</p>
                    </div>
                    <div style="text-align: center; background: var(--bg-light); padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-hospital" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Medical Centers</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem;">Mayo Clinic, Cleveland Clinic, Mass General Brigham</p>
                    </div>
                    <div style="text-align: center; background: var(--bg-light); padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-globe" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Global Organizations</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem;">WHO, Alzheimer's Association, ADI</p>
                    </div>
                    <div style="text-align: center; background: var(--bg-light); padding: 1.5rem; border-radius: 8px;">
                        <i class="fas fa-certificate" style="font-size: 2rem; color: var(--primary-blue); margin-bottom: 1rem;"></i>
                        <h5 style="color: var(--text-dark); margin-bottom: 0.5rem;">Regulatory Bodies</h5>
                        <p style="color: var(--text-gray); font-size: 0.9rem;">FDA, EMA, Health Canada, TGA</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; position: relative; overflow: hidden;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1000 1000\"><defs><radialGradient id=\"a\" cx=\"50%\" cy=\"50%\"><stop offset=\"0%\" stop-color=\"%23ffffff\" stop-opacity=\"0.1\"/><stop offset=\"100%\" stop-color=\"%23ffffff\" stop-opacity=\"0\"/></radialGradient></defs><circle cx=\"200\" cy=\"200\" r=\"100\" fill=\"url(%23a)\"/><circle cx=\"800\" cy=\"300\" r=\"150\" fill=\"url(%23a)\"/><circle cx=\"400\" cy=\"700\" r=\"120\" fill=\"url(%23a)\"/></svg>'); opacity: 0.3;"></div>
        <div class="container" style="position: relative; z-index: 1;">
            <h2 style="color: white; margin-bottom: 1rem; font-size: 2.5rem; font-weight: 700;">
                Join Us in Our Mission
            </h2>
            <p style="font-size: 1.2rem; opacity: 0.9; margin-bottom: 2rem; max-width: 600px; margin-left: auto; margin-right: auto; line-height: 1.6;">
                Whether you're a healthcare provider, researcher, or someone concerned about cognitive health, we invite you to be part of the solution.
            </p>
            <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                <button type="button" onclick="startScreening(); return false;"
                        style="background: white; color: var(--primary-blue); padding: 1rem 2rem; border: none;
                               border-radius: 50px; font-weight: 600; font-size: 1.1rem; cursor: pointer;
                               transition: all 0.3s ease; display: inline-flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-microphone"></i>
                    Try Our Screening
                </button>
                <a href="audio_upload/history/" style="display: inline-flex; align-items: center; gap: 0.5rem; padding: 1rem 2rem;
                                          background: rgba(255,255,255,0.1); color: white; text-decoration: none; border-radius: 50px;
                                          font-weight: 600; transition: all 0.3s ease; backdrop-filter: blur(10px);
                                          border: 1px solid rgba(255,255,255,0.2);">
                    <i class="fas fa-history"></i>
                    View History
                </a>
            </div>
        </div>
    </section>

<script>
    // Function to check if user is authenticated
    function isUserAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) {
            console.log('❌ No access token found');
            return false;
        }

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;

            if (payload.exp < currentTime) {
                console.log('❌ Token expired');
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                return false;
            }

            console.log('✅ User is authenticated');
            return true;
        } catch (error) {
            console.error('❌ Error checking token:', error);
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            return false;
        }
    }

    // Function to start screening
    function startScreening() {
        console.log('🎯 Starting screening process...');

        const isAuth = isUserAuthenticated();
        console.log('🔍 Authentication result:', isAuth);

        if (isAuth) {
            console.log('✅ User is authenticated, redirecting to audio upload');
            window.location.href = '/audio_upload/';
        } else {
            console.log('⚠️ User not authenticated, redirecting to login');
            // Store the intended destination
            sessionStorage.setItem('redirectAfterLogin', '/audio_upload/');
            console.log('💾 Stored redirect URL in sessionStorage');
            window.location.href = '/login/';
        }
    }

    // Make function available globally
    window.startScreening = startScreening;

    // Ensure function is available when page loads
    document.addEventListener('DOMContentLoaded', () => {
        window.startScreening = startScreening;
        console.log('✅ About page: startScreening function loaded');
    });
</script>

{% endblock %}
